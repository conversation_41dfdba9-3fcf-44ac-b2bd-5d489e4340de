<template>
  <div class="bg-white relative min-h-screen">
    <!-- Main Title -->
    <div class="flex flex-col items-center gap-[9px] mt-[115px] mb-10">
      <h1 class="font-bold text-[40px] text-[#4a4a4a] text-center leading-[1.2]">
        <PERSON><PERSON><PERSON> nhận thông tin mua bảo hiểm
      </h1>
      <p class="text-[16px] text-[#4a4a4a] text-center">
        Vui lòng kiểm tra lại thông tin trước khi thanh toán.
      </p>
    </div>

    <!-- Progress Steps -->
    <div class="flex items-center justify-center mb-10">
      <div class="flex items-center">
        <!-- Step 1 - Completed -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="w-[60px] h-[60px] bg-[#3079ff] rounded-[30px] flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <div class="text-[#3079ff] font-bold text-[16px] text-center">
            Khai báo thông tin mua bảo hiểm
          </div>
        </div>

        <!-- Line 1 -->
        <div class="w-[257px] h-[2px] bg-[#3079ff] mx-4"></div>

        <!-- Step 2 - Active -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="w-[60px] h-[60px] bg-[#3079ff] rounded-[30px] flex items-center justify-center">
            <span class="text-white font-bold text-[28px]">2</span>
          </div>
          <div class="text-[#3079ff] font-bold text-[16px] text-center">
            Xác nhận thông tin
          </div>
        </div>

        <!-- Line 2 -->
        <div class="w-[253px] h-[2px] bg-gray-300 mx-4"></div>

        <!-- Step 3 - Inactive -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="w-[60px] h-[60px] border-2 border-[#3079ff] rounded-[30px] flex items-center justify-center">
            <span class="text-[#3079ff] font-bold text-[28px]">3</span>
          </div>
          <div class="text-black text-[16px] text-center">
            Thanh toán
          </div>
        </div>
      </div>
    </div>

    <!-- Content Container -->
    <div class="bg-white rounded-lg border border-[#e9ecee] mx-[392px] mb-[121.75px] p-8">
      <div class="space-y-8">
        <!-- Thông tin chủ xe -->
        <div>
          <h2 class="text-[20px] font-bold text-[#333] mb-6">Thông tin chủ xe</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Tên công ty</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.company_name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Mã số thuế</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.tax_number }}</p>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-500">Địa chỉ công ty</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.company_address }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Số điện thoại</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.phone_number }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Email nhận GCN</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.email_gcn }}</p>
            </div>
          </div>
        </div>

        <!-- Thông tin xe -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin xe</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Biển kiểm soát</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.bien_so_xe }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Số chỗ ngồi</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.so_cho_ngoi }}</p>
            </div>
            <div v-if="insuranceStore.vehicleInfo.so_khung">
              <label class="block text-sm font-medium text-gray-500">Số khung</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.so_khung }}</p>
            </div>
            <div v-if="insuranceStore.vehicleInfo.so_may">
              <label class="block text-sm font-medium text-gray-500">Số máy</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.so_may }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Trọng tải</label>
              <p class="text-gray-900 font-medium">Trên 15 tấn</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Loại xe</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.loai_xe }}</p>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-500">Mục đích sử dụng</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.md_su_dung }}</p>
            </div>
          </div>
        </div>

        <!-- Thời hạn bảo hiểm -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thời hạn bảo hiểm</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Ngày bắt đầu</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.formattedStartDate }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Ngày kết thúc</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.formattedEndDate }}</p>
            </div>
          </div>
        </div>

        <!-- Phí bảo hiểm -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Phí bảo hiểm</h2>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-gray-700">Phí chưa VAT:</span>
              <span class="font-medium">{{ insuranceStore.formattedFees.feeWithoutVat }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700">Thuế VAT (10%):</span>
              <span class="font-medium">{{ insuranceStore.formattedFees.vatAmount }}</span>
            </div>
            <div class="flex justify-between items-center border-t pt-3">
              <span class="text-gray-700">Tổng phí (gồm VAT):</span>
              <span class="font-medium">{{ insuranceStore.formattedFees.totalFeeWithVat }}</span>
            </div>
            <div class="flex justify-between items-center border-t pt-3">
              <span class="text-lg font-semibold text-gray-900">TỔNG PHÍ THANH TOÁN:</span>
              <span class="text-lg font-bold text-red-600">{{ insuranceStore.formattedFees.totalPayment }}</span>
            </div>
          </div>
        </div>

        <!-- Checkbox xác nhận -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-start space-x-3">
            <input
              type="checkbox"
              v-model="insuranceStore.isAgreed"
              class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
            />
            <div class="flex-1">
              <p class="text-gray-700">
                Tôi đồng ý đã đọc, hiểu các quy định Pháp luật về Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô
                <a 
                  href="#" 
                  @click.prevent="openInsuranceRegulations"
                  class="text-blue-600 hover:text-blue-800 underline ml-1"
                >
                  (Xem quy định bảo hiểm)
                </a>
              </p>
            </div>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end space-x-4 pt-6">
          <button
            type="button"
            @click="proceedToPayment"
            :disabled="!insuranceStore.isStep2Valid || isLoading"
            class="px-8 py-3 bg-[#3079ff] text-white rounded-[8px] hover:bg-[#2563eb] focus:outline-none focus:ring-2 focus:ring-[#3079ff] disabled:opacity-50 disabled:cursor-not-allowed font-medium text-[16px]"
          >
            <span v-if="isLoading">Đang xử lý...</span>
            <span v-else>Thanh toán</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal quy định bảo hiểm -->
    <div v-if="showRegulationsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-2xl max-h-[80vh] overflow-y-auto p-6 m-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Quy định Bảo hiểm bắt buộc trách nhiệm dân sự</h3>
          <button
            @click="closeRegulationsModal"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="prose max-w-none">
          <p class="text-gray-700 mb-4">
            Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe cơ giới là loại bảo hiểm nhằm bảo vệ lợi ích của người thứ ba 
            bị thiệt hại do xe cơ giới gây ra.
          </p>
          <h4 class="font-semibold mb-2">Phạm vi bảo hiểm:</h4>
          <ul class="list-disc pl-5 mb-4">
            <li>Thiệt hại về người: tối đa 150 triệu đồng/người/vụ tai nạn</li>
            <li>Thiệt hại về tài sản: tối đa 50 triệu đồng/vụ tai nạn</li>
          </ul>
          <h4 class="font-semibold mb-2">Trách nhiệm của chủ xe:</h4>
          <ul class="list-disc pl-5 mb-4">
            <li>Mua bảo hiểm bắt buộc trước khi đưa xe vào sử dụng</li>
            <li>Duy trì hiệu lực bảo hiểm trong suốt thời gian sử dụng xe</li>
            <li>Thông báo kịp thời cho công ty bảo hiểm khi xảy ra tai nạn</li>
          </ul>
        </div>
        <div class="flex justify-end mt-6">
          <button
            @click="closeRegulationsModal"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Xác nhận thông tin mua bảo hiểm'
})

const insuranceStore = useInsuranceStore()
const isLoading = ref(false)
const showRegulationsModal = ref(false)

// Kiểm tra nếu chưa có thông tin từ bước 1
onMounted(() => {
  if (!insuranceStore.isStep1Valid) {
    navigateTo('/insurance/step1')
  }
})

// Mở modal quy định bảo hiểm
const openInsuranceRegulations = () => {
  showRegulationsModal.value = true
}

// Đóng modal quy định bảo hiểm
const closeRegulationsModal = () => {
  showRegulationsModal.value = false
}

// Quay lại bước 1
const goBack = () => {
  navigateTo('/insurance/step1')
}

// Tiến hành thanh toán
const proceedToPayment = async () => {
  if (!insuranceStore.isStep2Valid) {
    return
  }

  isLoading.value = true

  try {
    const { createInsuranceOrder } = useInsuranceApi()
    const result = await createInsuranceOrder()
    const response = await post('/v1/orders/buy-insurance')

    if (response.data.success) {
      // Chuyển đến bước thanh toán
      setTimeout(() => {
        navigateTo('/insurance/step3')
      }, 1500)
    }
  } catch (error) {
    console.error('Error creating order:', error)
  } finally {
    isLoading.value = false
  }
}
</script>
